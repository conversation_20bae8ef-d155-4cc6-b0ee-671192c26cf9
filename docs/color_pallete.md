# Crescer Feliz – Website Color Palette

A clean, modern palette based on the <PERSON><PERSON>cer Feliz logo, emphasizing minty greens and warm yellows
for a friendly, fresh look.

---

## 🎨 Primary Colors

- **Mint Green** – `#7ACBC1`  
  Fresh, calm, modern. Great for headers, buttons, or backgrounds.

- **Warm Yellow** – `#F6C75A`  
  Friendly and optimistic. Use for accents like CTAs, highlights, and icons.

---

## 🌿 Secondary / Supporting Colors

- **Soft Sage** – `#A8DEDA`  
  A lighter mint variant for hover states, background panels, or overlays.

- **Pale Cream** – `#F5EFE9`  
  Clean neutral for page backgrounds or large sections—keeps it easy on the eyes.

---

## 🔧 Accent / Utility Colors

- **Warm Gray** – `#A8A096`  
  Use for body text, dividers, or footers. Keeps contrast high without harshness.

- **Coral Pink** – `#EF8A7E`  
  Optional pop color when you need a draw-away or error state that's still friendly.

---

## 📋 Color Palette Table

| Role                     | Color       | Hex       |
| ------------------------ | ----------- | --------- |
| Primary Mint             | Mint Green  | `#7ACBC1` |
| Primary Accent           | Warm Yellow | `#F6C75A` |
| Light Variant            | Soft Sage   | `#A8DEDA` |
| Background Neutral       | Pale Cream  | `#F5EFE9` |
| Text / Secondary Usage   | Warm Gray   | `#A8A096` |
| Optional Attention Color | Coral Pink  | `#EF8A7E` |

---

## 📌 Usage Strategy

- **Headers / Nav Bars**: Mint Green (`#7ACBC1`) for vibrancy and brand alignment.
- **CTAs / Icons / Highlights**: Warm Yellow (`#F6C75A`) to attract eyes without shouting.
- **Hover / Subtle Overlays**: Soft Sage (`#A8DEDA`) for nuanced interactive feedback.
- **Backgrounds**: Pale Cream (`#F5EFE9`) keeps pages looking clean and elevated.
- **Text**: Warm Gray (`#A8A096`) ensures readability and ties into the earth-friendly vibe.
- **Alerts / Promo Pushes**: Coral Pink (`#EF8A7E`) works as a contrasting but cohesive accent.

---

**Pro Tip:** Stick to **3–4 colors** per view to avoid visual clutter.  
Always check accessibility contrast with tools like [axe](https://www.deque.com/axe/) or
[Contrast Checker](https://contrastchecker.com/).

---

## 🌗 CSS Variable Tokens (Light Theme)

These are declared in `src/app/globals.css` under `:root`:

| Token                  | Value                   | Purpose                      |
| ---------------------- | ----------------------- | ---------------------------- |
| `--brand-mint`         | `#7acbc1`               | Primary brand / actions      |
| `--brand-yellow`       | `#f6c75a`               | Accents / highlight          |
| `--brand-sage`         | `#a8deda`               | Hovers / soft surfaces       |
| `--brand-cream`        | `#f5efe9`               | App background               |
| `--brand-gray`         | `#a8a096`               | Muted text / secondary       |
| `--brand-coral`        | `#ef8a7e`               | Attention / warning alt      |
| `--color-bg`           | `var(--brand-cream)`    | Page background              |
| `--color-bg-surface`   | `rgba(255,255,255,0.7)` | Translucent panels           |
| `--color-bg-elevated`  | `#ffffff`               | Elevated cards/dialogs       |
| `--color-border`       | `var(--brand-sage)`     | Neutral borders              |
| `--color-ring`         | `var(--brand-mint)`     | Focus ring                   |
| `--color-text-primary` | `#1f2937`               | Primary text                 |
| `--color-text-muted`   | `#6b7280`               | Muted text                   |
| `--color-text-invert`  | `#ffffff`               | Text over dark/mint surfaces |

## 🌑 Dark Theme Overrides

Declared under `.dark` (or `[data-theme='dark']`):

| Token                  | Value (Dark)         | Note                            |
| ---------------------- | -------------------- | ------------------------------- |
| `--brand-yellow`       | `#d8ad49`            | Slightly dimmed for luminance   |
| `--brand-sage`         | `#79c2bc`            | Deepened to keep hover contrast |
| `--brand-cream`        | `#0f1514`            | Acts as dark bg base            |
| `--brand-gray`         | `#cfc8bf`            | Lightened for legibility        |
| `--brand-coral`        | `#f08d81`            | Micro tweak for dark contrast   |
| `--color-bg`           | `var(--brand-cream)` | Page background                 |
| `--color-bg-surface`   | `#182220`            | Surface 1                       |
| `--color-bg-elevated`  | `#1f2b29`            | Surface 2 (cards)               |
| `--color-border`       | `#27413d`            | Subtle border                   |
| `--color-text-primary` | `#f3f4f6`            | Primary text                    |
| `--color-text-muted`   | `#9ca3af`            | Muted text                      |
| `--color-text-invert`  | `#0f1514`            | For inverted contexts           |

## 🧩 Tailwind Mapping (tailwind.config.js)

Custom colors reference the same CSS variables so switching theme is _zero-cost_:

```js
extend: {
  colors: {
    brand: {
      mint: 'var(--brand-mint)',
      yellow: 'var(--brand-yellow)',
      sage: 'var(--brand-sage)',
      cream: 'var(--brand-cream)',
      gray: 'var(--brand-gray)',
      coral: 'var(--brand-coral)',
    },
    surface: 'var(--color-bg-surface)',
    elevated: 'var(--color-bg-elevated)',
    border: 'var(--color-border)',
    ring: 'var(--color-ring)',
    body: 'var(--color-bg)'
  },
  textColor: {
    primary: 'var(--color-text-primary)',
    muted: 'var(--color-text-muted)',
    invert: 'var(--color-text-invert)',
    brand: 'var(--brand-mint)'
  }
}
```

## 🔄 Theme Switching

- Control by toggling `.dark` on `<html>` or `<body>`.
- Implementado via `ThemeProvider` (`useTheme()`, `ThemeToggle`).
- Respeita `prefers-color-scheme` e guarda escolha em `localStorage` (`cf-theme`).

### Quick Example

```tsx
import { ThemeToggle } from '@/components/common';

export function Toolbar() {
  return <ThemeToggle className='ml-auto' />;
}
```

## ✅ Usage Guidelines

| Use Case          | Recommended Tokens                               |
| ----------------- | ------------------------------------------------ |
| Primary CTA       | bg-brand-mint text-white or gradient mint→yellow |
| Secondary Button  | bg-surface border border-border text-primary     |
| Card Background   | bg-surface dark:bg-surface                       |
| Page Background   | body (var(--color-bg))                           |
| Focus Ring        | focus-visible:ring-brand                         |
| Warning/Attention | text-brand-coral / bg-brand-coral/10             |

## ♿ Accessibility Notes

| Pair                              | Contrast (approx) | Status                      |
| --------------------------------- | ----------------- | --------------------------- |
| Mint (#7ACBC1) on Cream (#F5EFE9) | >3:1              | Large text / UI OK          |
| Primary text (#1F2937) on Cream   | >7:1              | AAA                         |
| Coral (#EF8A7E) on Cream          | ~3.5:1            | Consider bold for body text |
| Yellow (#F6C75A) on White         | ~2:1              | Use only for accents/icons  |

Always validate critical text (esp. small sizes). Avoid yellow for small text over white/cream.

## 🛠 Migration Tips

1. Prefer semantic classes (e.g. `bg-surface`, `text-primary`) over direct brand hex.
2. Use `brand-sage` for subtle hover backgrounds instead of lowering opacity of mint (keeps
   consistency).
3. For gradients: `from-brand-mint to-brand-yellow` – works in both themes because variables adapt.
4. Avoid mixing too many transparencies on dark surfaces (can produce muddy tones); prefer solid
   semantic surfaces.

---

Última atualização: sincronizado com tokens e ThemeProvider (dark mode).
