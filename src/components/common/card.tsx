'use client';

import { motion } from 'framer-motion';
import React from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

/**
 * Card component variants using tailwind-variants
 */
export const cardVariants = tv({
  base: [
    'relative rounded-lg transition-all duration-200',
    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
  ],
  variants: {
    variant: {
      default: [
        'bg-surface border border-border',
        'dark:bg-surface dark:border-border',
      ],
      elevated: [
        'bg-elevated border border-border shadow-sm',
        'dark:bg-elevated dark:border-border',
      ],
      outline: ['bg-transparent border border-border', 'dark:border-border'],
      ghost: ['bg-transparent border-transparent'],
      gradient: [
        'bg-gradient-to-br from-surface to-elevated border border-border',
        'dark:from-surface dark:to-elevated dark:border-border',
      ],
    },
    padding: {
      none: 'p-0',
      sm: 'p-3',
      md: 'p-4',
      lg: 'p-6',
      xl: 'p-8',
    },
    shadow: {
      none: '',
      sm: 'shadow-sm',
      md: 'shadow-md',
      lg: 'shadow-lg',
      xl: 'shadow-xl',
    },
    interactive: {
      true: [
        'cursor-pointer',
        'hover:shadow-md hover:scale-[1.02]',
        'active:scale-[0.98]',
        'transition-all duration-200',
      ],
    },
    size: {
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl',
      full: 'w-full',
    },
  },
  defaultVariants: {
    variant: 'default',
    padding: 'md',
    shadow: 'sm',
    size: 'full',
  },
});

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  /**
   * Whether to animate the card entrance
   */
  animate?: boolean;
}

/**
 * Card component with variants and animations
 *
 * A flexible container component that provides consistent styling and layout
 * for content grouping. Supports multiple visual variants, interactive states,
 * and smooth animations for enhanced user experience.
 *
 * @component
 * @example
 * // Basic card with content
 * <Card variant="elevated">
 *   <CardHeader>
 *     <CardTitle>Product Overview</CardTitle>
 *     <CardDescription>Detailed information about our product</CardDescription>
 *   </CardHeader>
 *   <CardContent>
 *     <p>This is the main content of the card...</p>
 *   </CardContent>
 *   <CardFooter>
 *     <Button variant="primary">Learn More</Button>
 *   </CardFooter>
 * </Card>
 *
 * @example
 * // Interactive card with click handler
 * <Card variant="outline" interactive onClick={handleCardClick}>
 *   <CardContent>
 *     <h3>Clickable Card</h3>
 *     <p>This entire card is clickable</p>
 *   </CardContent>
 * </Card>
 *
 * @example
 * // Card with image
 * <Card variant="default">
 *   <CardImage src="/image.jpg" alt="Product image" />
 *   <CardHeader>
 *     <CardTitle>Product Name</CardTitle>
 *   </CardHeader>
 *   <CardContent>
 *     Product description goes here...
 *   </CardContent>
 * </Card>
 *
 * @param {CardProps} props - The card component props
 * @param {CardVariant} [props.variant="default"] - Visual style variant
 * @param {boolean} [props.interactive=false] - Whether card is clickable/hoverable
 * @param {boolean} [props.animate=true] - Whether to animate card entrance
 * @param {string} [props.className] - Additional CSS classes
 * @param {React.ReactNode} props.children - Card content
 * @param {function} [props.onClick] - Click event handler for interactive cards
 *
 * @accessibility
 * - Supports keyboard navigation when interactive
 * - Provides proper focus management and visual indicators
 * - Maintains semantic structure with proper heading hierarchy
 * - Includes ARIA attributes for interactive states
 *
 * @performance
 * - Conditional animation rendering based on animate prop
 * - Optimized hover and focus states
 * - Efficient re-rendering through proper prop handling
 *
 * @returns {React.ForwardRefExoticComponent} Forwarded ref card component
 */
export const Card = React.forwardRef<HTMLDivElement, CardProps>(
  (
    {
      className,
      variant,
      padding,
      shadow,
      interactive,
      size,
      animate = true,
      onClick,
      ...props
    },
    ref
  ) => {
    const isInteractive = interactive || Boolean(onClick);

    const motionProps = isInteractive
      ? {
          whileHover: { y: -2 },
          whileTap: { scale: 0.98 },
        }
      : {};

    if (animate) {
      return (
        <motion.div
          ref={ref}
          className={cardVariants({
            variant,
            padding,
            shadow,
            interactive: isInteractive,
            size,
            className,
          })}
          onClick={onClick}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          {...motionProps}
          {...(props as any)}
        />
      );
    }

    return (
      <div
        ref={ref}
        className={cardVariants({
          variant,
          padding,
          shadow,
          interactive: isInteractive,
          size,
          className,
        })}
        onClick={onClick}
        {...props}
      />
    );
  }
);
Card.displayName = 'Card';

export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}
export const CardHeader: React.FC<CardHeaderProps> = ({
  className,
  ...rest
}) => (
  <div
    className={`mb-3 flex flex-col space-y-1 ${className || ''}`}
    {...rest}
  />
);

export interface CardTitleProps
  extends React.HTMLAttributes<HTMLHeadingElement> {}
export const CardTitle: React.FC<CardTitleProps> = ({
  className,
  children,
  ...rest
}) => (
  <h3
    className={`text-base font-semibold tracking-tight text-primary ${className || ''}`}
    {...rest}
  >
    {children}
  </h3>
);

export interface CardDescriptionProps
  extends React.HTMLAttributes<HTMLParagraphElement> {}
export const CardDescription: React.FC<CardDescriptionProps> = ({
  className,
  ...rest
}) => <p className={`text-sm text-muted ${className || ''}`} {...rest} />;

export interface CardContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}
export const CardContent: React.FC<CardContentProps> = ({
  className,
  ...rest
}) => <div className={`space-y-3 ${className || ''}`} {...rest} />;

export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}
export const CardFooter: React.FC<CardFooterProps> = ({
  className,
  ...rest
}) => (
  <div
    className={`mt-4 flex items-center justify-end space-x-2 ${className || ''}`}
    {...rest}
  />
);

/**
 * Card image component
 */
export interface CardImageProps
  extends React.ImgHTMLAttributes<HTMLImageElement> {
  /**
   * Aspect ratio of the image
   */
  aspectRatio?: 'square' | 'video' | 'wide' | 'auto';
}

export const CardImage: React.FC<CardImageProps> = ({
  className,
  aspectRatio = 'auto',
  ...props
}) => {
  const aspectRatioClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    wide: 'aspect-[21/9]',
    auto: '',
  };

  return (
    <div
      className={`overflow-hidden rounded-t-lg ${aspectRatioClasses[aspectRatio]}`}
    >
      <img
        className={`h-full w-full object-cover ${className || ''}`}
        {...props}
      />
    </div>
  );
};

export default Card;
