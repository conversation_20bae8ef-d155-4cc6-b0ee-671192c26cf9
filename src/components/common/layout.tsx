'use client';

import React from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

/**
 * Container component variants
 */
export const containerVariants = tv({
  base: 'mx-auto px-4 sm:px-6 lg:px-8',
  variants: {
    size: {
      sm: 'max-w-2xl',
      md: 'max-w-4xl',
      lg: 'max-w-6xl',
      xl: 'max-w-7xl',
      full: 'max-w-full',
    },
    padding: {
      none: 'px-0',
      sm: 'px-4',
      md: 'px-4 sm:px-6 lg:px-8',
      lg: 'px-6 sm:px-8 lg:px-12',
    },
  },
  defaultVariants: {
    size: 'lg',
    padding: 'md',
  },
});

/**
 * Stack component variants
 */
export const stackVariants = tv({
  base: 'flex',
  variants: {
    direction: {
      row: 'flex-row',
      column: 'flex-col',
      'row-reverse': 'flex-row-reverse',
      'column-reverse': 'flex-col-reverse',
    },
    spacing: {
      none: 'gap-0',
      xs: 'gap-1',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8',
      '2xl': 'gap-12',
    },
    align: {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      stretch: 'items-stretch',
      baseline: 'items-baseline',
    },
    justify: {
      start: 'justify-start',
      center: 'justify-center',
      end: 'justify-end',
      between: 'justify-between',
      around: 'justify-around',
      evenly: 'justify-evenly',
    },
    wrap: {
      true: 'flex-wrap',
      false: 'flex-nowrap',
    },
  },
  defaultVariants: {
    direction: 'column',
    spacing: 'md',
    align: 'stretch',
    justify: 'start',
    wrap: false,
  },
});

/**
 * Grid component variants
 */
export const gridVariants = tv({
  base: 'grid',
  variants: {
    cols: {
      1: 'grid-cols-1',
      2: 'grid-cols-2',
      3: 'grid-cols-3',
      4: 'grid-cols-4',
      5: 'grid-cols-5',
      6: 'grid-cols-6',
      12: 'grid-cols-12',
    },
    gap: {
      none: 'gap-0',
      xs: 'gap-1',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8',
      '2xl': 'gap-12',
    },
    responsive: {
      true: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    },
  },
  defaultVariants: {
    cols: 1,
    gap: 'md',
  },
});

export interface ContainerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof containerVariants> {}

/**
 * Container component for consistent page layouts
 * 
 * @example
 * ```tsx
 * <Container size="lg">
 *   <h1>Page Title</h1>
 *   <p>Page content...</p>
 * </Container>
 * ```
 */
export const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ className, size, padding, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={containerVariants({ size, padding, className })}
        {...props}
      />
    );
  }
);
Container.displayName = 'Container';

export interface StackProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof stackVariants> {}

/**
 * Stack component for flexible layouts with consistent spacing
 * 
 * @example
 * ```tsx
 * <Stack direction="row" spacing="lg" align="center">
 *   <Button>Cancel</Button>
 *   <Button variant="primary">Save</Button>
 * </Stack>
 * ```
 */
export const Stack = React.forwardRef<HTMLDivElement, StackProps>(
  ({ className, direction, spacing, align, justify, wrap, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={stackVariants({
          direction,
          spacing,
          align,
          justify,
          wrap,
          className,
        })}
        {...props}
      />
    );
  }
);
Stack.displayName = 'Stack';

export interface GridProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof gridVariants> {}

/**
 * Grid component for responsive grid layouts
 * 
 * @example
 * ```tsx
 * <Grid cols={3} gap="lg">
 *   <Card>Item 1</Card>
 *   <Card>Item 2</Card>
 *   <Card>Item 3</Card>
 * </Grid>
 * ```
 */
export const Grid = React.forwardRef<HTMLDivElement, GridProps>(
  ({ className, cols, gap, responsive, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={gridVariants({ cols, gap, responsive, className })}
        {...props}
      />
    );
  }
);
Grid.displayName = 'Grid';

/**
 * Flex component - alias for Stack with row direction
 */
export interface FlexProps extends Omit<StackProps, 'direction'> {
  direction?: 'row' | 'row-reverse';
}

export const Flex = React.forwardRef<HTMLDivElement, FlexProps>(
  ({ direction = 'row', ...props }, ref) => {
    return <Stack ref={ref} direction={direction} {...props} />;
  }
);
Flex.displayName = 'Flex';

/**
 * Center component for centering content
 */
export interface CenterProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Whether to center both horizontally and vertically
   */
  both?: boolean;
  /**
   * Minimum height when centering both directions
   */
  minHeight?: string;
}

export const Center: React.FC<CenterProps> = ({
  className,
  both = false,
  minHeight = '100vh',
  style,
  ...props
}) => {
  const centerClasses = both
    ? 'flex items-center justify-center'
    : 'flex justify-center';

  return (
    <div
      className={`${centerClasses} ${className || ''}`}
      style={{
        ...(both && { minHeight }),
        ...style,
      }}
      {...props}
    />
  );
};
Center.displayName = 'Center';

/**
 * Spacer component for adding flexible space
 */
export interface SpacerProps extends React.HTMLAttributes<HTMLDivElement> {}

export const Spacer: React.FC<SpacerProps> = ({ className, ...props }) => {
  return <div className={`flex-1 ${className || ''}`} {...props} />;
};
Spacer.displayName = 'Spacer';

/**
 * Divider component for visual separation
 */
export interface DividerProps extends React.HTMLAttributes<HTMLHRElement> {
  /**
   * Orientation of the divider
   */
  orientation?: 'horizontal' | 'vertical';
  /**
   * Text label for the divider
   */
  label?: string;
  /**
   * Position of the label
   */
  labelPosition?: 'left' | 'center' | 'right';
}

export const Divider: React.FC<DividerProps> = ({
  className,
  orientation = 'horizontal',
  label,
  labelPosition = 'center',
  ...props
}) => {
  if (label) {
    const labelPositionClasses = {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
    };

    return (
      <div className={`relative ${className || ''}`}>
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-border" />
        </div>
        <div className={`relative flex justify-center ${labelPositionClasses[labelPosition]}`}>
          <span className="bg-body px-2 text-sm text-muted">{label}</span>
        </div>
      </div>
    );
  }

  if (orientation === 'vertical') {
    return (
      <hr
        className={`border-l border-border h-full w-0 ${className || ''}`}
        {...props}
      />
    );
  }

  return (
    <hr
      className={`border-t border-border w-full ${className || ''}`}
      {...props}
    />
  );
};
Divider.displayName = 'Divider';
