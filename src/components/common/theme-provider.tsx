'use client';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextValue {
  theme: Theme;
  setTheme: (t: Theme) => void;
  toggleTheme: () => void;
  resolved: boolean;
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

const STORAGE_KEY = 'cf-theme';

function getPreferredTheme(): Theme {
  if (typeof window === 'undefined') {
    return 'light';
  }
  const stored = window.localStorage.getItem(STORAGE_KEY) as Theme | null;
  if (stored === 'light' || stored === 'dark') {
    return stored;
  }
  const mql = window.matchMedia('(prefers-color-scheme: dark)');
  return mql.matches ? 'dark' : 'light';
}

export const ThemeProvider: React.FC<{
  children: React.ReactNode;
  defaultTheme?: Theme;
}> = ({ children, defaultTheme = 'light' }) => {
  const [theme, setThemeState] = useState<Theme>(defaultTheme);
  const [resolved, setResolved] = useState(false);

  const apply = useCallback((t: Theme) => {
    const root = document.documentElement;
    if (t === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, []);

  const setTheme = useCallback(
    (t: Theme) => {
      setThemeState(t);
      try {
        window.localStorage.setItem(STORAGE_KEY, t);
      } catch {}
      apply(t);
    },
    [apply]
  );

  const toggleTheme = useCallback(() => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  }, [theme, setTheme]);

  useEffect(() => {
    const initial = getPreferredTheme();
    setThemeState(initial);
    apply(initial);
    setResolved(true);
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    const listener = (e: MediaQueryListEvent) => {
      const stored = window.localStorage.getItem(STORAGE_KEY);
      if (!stored) {
        setTheme(e.matches ? 'dark' : 'light');
      }
    };
    try {
      mql.addEventListener('change', listener);
    } catch {
      /* safari */
    }
    return () => {
      try {
        mql.removeEventListener('change', listener);
      } catch {}
    };
  }, [apply, setTheme]);

  const value: ThemeContextValue = { theme, setTheme, toggleTheme, resolved };
  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
};

export function useTheme() {
  const ctx = useContext(ThemeContext);
  if (!ctx) {
    throw new Error('useTheme must be used within ThemeProvider');
  }
  return ctx;
}

export const ThemeToggle: React.FC<{
  className?: string;
  labelLight?: string;
  labelDark?: string;
}> = ({ className = '', labelLight = 'Claro', labelDark = 'Escuro' }) => {
  const { theme, toggleTheme, resolved } = useTheme();
  return (
    <button
      type='button'
      onClick={toggleTheme}
      aria-label='Alternar tema'
      className={`inline-flex items-center gap-2 rounded-md border border-brand-mint/40 px-3 py-2 text-sm font-medium text-brand-mint bg-white/50 dark:bg-elevated dark:text-brand-mint hover:bg-brand-sage/30 dark:hover:bg-brand-sage/20 transition-colors ${className}`}
    >
      <span className='relative inline-flex w-4 h-4'>
        <span className='absolute inset-0 transition-opacity duration-300 rounded-full shadow-sm bg-brand-mint dark:opacity-0' />
        <span className='absolute inset-0 transition-opacity duration-300 rounded-full shadow-sm opacity-0 dark:opacity-100 bg-brand-yellow' />
      </span>
      {resolved ? (theme === 'dark' ? labelDark : labelLight) : '...'}
    </button>
  );
};

export default ThemeProvider;
