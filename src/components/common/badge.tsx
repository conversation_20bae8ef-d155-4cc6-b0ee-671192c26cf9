'use client';

import { motion } from 'framer-motion';
import React from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

/**
 * Badge component variants using tailwind-variants
 */
export const badgeVariants = tv({
  base: [
    'inline-flex items-center gap-1 rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200',
    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
  ],
  variants: {
    variant: {
      default: [
        'bg-surface border border-border text-primary',
        'hover:bg-elevated',
        'dark:bg-surface dark:border-border dark:text-primary',
      ],
      primary: [
        'bg-brand-mint text-white',
        'hover:bg-brand-sage',
        'dark:bg-brand-mint dark:text-white',
      ],
      secondary: [
        'bg-brand-sage/20 text-brand-mint border border-brand-sage/30',
        'hover:bg-brand-sage/30',
        'dark:bg-brand-sage/20 dark:text-brand-mint',
      ],
      success: [
        // Success uses brand-mint (text) with subtle mint background & border
        'bg-brand-mint/10 text-brand-mint border border-brand-mint/30',
        'hover:bg-brand-mint/15',
        'dark:bg-brand-mint/15 dark:text-brand-mint dark:border-brand-mint/30',
      ],
      warning: [
        'bg-brand-yellow/15 text-brand-yellow border border-brand-yellow/30',
        'hover:bg-brand-yellow/25',
        'dark:bg-brand-yellow/20 dark:text-brand-yellow dark:border-brand-yellow/30',
      ],
      error: [
        'bg-brand-coral/15 text-brand-coral border border-brand-coral/30',
        'hover:bg-brand-coral/25',
        'dark:bg-brand-coral/20 dark:text-brand-coral dark:border-brand-coral/30',
      ],
      outline: [
        'border border-border text-primary bg-transparent',
        'hover:bg-surface',
        'dark:border-border dark:text-primary',
      ],
    },
    size: {
      sm: 'px-2 py-0.5 text-xs',
      md: 'px-2.5 py-0.5 text-xs',
      lg: 'px-3 py-1 text-sm',
    },
    interactive: {
      true: 'cursor-pointer hover:scale-105 active:scale-95',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'md',
  },
});

export interface BadgeProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof badgeVariants> {
  /**
   * Icon to display before the badge text
   */
  icon?: React.ReactNode;
  /**
   * Icon to display after the badge text
   */
  endIcon?: React.ReactNode;
  /**
   * Whether the badge is removable (shows close button)
   */
  removable?: boolean;
  /**
   * Callback when remove button is clicked
   */
  onRemove?: () => void;
  /**
   * Whether to animate the badge entrance
   */
  animate?: boolean;
}

/**
 * Badge component for displaying status, categories, or labels
 *
 * A compact component for displaying short pieces of information such as status,
 * categories, counts, or labels. Supports multiple variants, icons, and interactive
 * features like removal functionality.
 *
 * @component
 * @example
 * // Basic status badges
 * <Badge variant="primary">New</Badge>
 * <Badge variant="success">Completed</Badge>
 * <Badge variant="warning">Pending</Badge>
 * <Badge variant="error">Failed</Badge>
 *
 * @example
 * // Badge with icons
 * <Badge variant="success" icon={<CheckIcon />}>
 *   Verified
 * </Badge>
 * <Badge variant="info" endIcon={<ArrowIcon />}>
 *   View Details
 * </Badge>
 *
 * @example
 * // Removable badges (tags)
 * <Badge variant="outline" removable onRemove={handleRemove}>
 *   React
 * </Badge>
 * <Badge variant="secondary" removable onRemove={handleRemove}>
 *   TypeScript
 * </Badge>
 *
 * @example
 * // Interactive badges
 * <Badge variant="primary" interactive onClick={handleClick}>
 *   Clickable Badge
 * </Badge>
 *
 * @example
 * // Different sizes
 * <Badge size="sm" variant="primary">Small</Badge>
 * <Badge size="md" variant="primary">Medium</Badge>
 * <Badge size="lg" variant="primary">Large</Badge>
 *
 * @param {BadgeProps} props - The badge component props
 * @param {BadgeVariant} [props.variant="default"] - Visual style variant
 * @param {BadgeSize} [props.size="md"] - Size of the badge
 * @param {boolean} [props.interactive=false] - Whether badge is clickable
 * @param {React.ReactNode} [props.icon] - Icon to display before text
 * @param {React.ReactNode} [props.endIcon] - Icon to display after text
 * @param {boolean} [props.removable=false] - Whether to show remove button
 * @param {function} [props.onRemove] - Callback when remove button is clicked
 * @param {boolean} [props.animate=true] - Whether to animate badge entrance
 * @param {string} [props.className] - Additional CSS classes
 * @param {React.ReactNode} props.children - Badge content
 * @param {function} [props.onClick] - Click event handler
 *
 * @accessibility
 * - Supports keyboard navigation for interactive and removable badges
 * - Provides proper ARIA labels for remove buttons
 * - Maintains focus management during removal
 * - Includes screen reader announcements for state changes
 *
 * @performance
 * - Conditional rendering of icons and remove buttons
 * - Optimized animations with Framer Motion
 * - Efficient event handling for interactive states
 *
 * @returns {React.ForwardRefExoticComponent} Forwarded ref badge component
 */
export const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  (
    {
      className,
      variant,
      size,
      interactive,
      icon,
      endIcon,
      removable,
      onRemove,
      animate = true,
      children,
      onClick,
      style,
      ...props
    },
    ref
  ) => {
    const isInteractive = interactive || Boolean(onClick) || removable;

    const badgeContent = (
      <>
        {icon && <span className='inline-flex items-center'>{icon}</span>}
        {children && <span>{children}</span>}
        {endIcon && <span className='inline-flex items-center'>{endIcon}</span>}
        {removable && (
          <button
            type='button'
            onClick={(e) => {
              e.stopPropagation();
              onRemove?.();
            }}
            className='ml-1 inline-flex items-center justify-center w-3 h-3 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition-colors'
            aria-label='Remove'
          >
            <svg
              className='w-2 h-2'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M6 18L18 6M6 6l12 12'
              />
            </svg>
          </button>
        )}
      </>
    );

    if (animate) {
      const motionProps = {
        initial: { opacity: 0, scale: 0.8 },
        animate: { opacity: 1, scale: 1 },
        exit: { opacity: 0, scale: 0.8 },
        transition: { duration: 0.2 },
        ...(isInteractive && {
          whileHover: { scale: 1.05 },
          whileTap: { scale: 0.95 },
        }),
      };

      return (
        <motion.span
          ref={ref}
          className={badgeVariants({
            variant,
            size,
            interactive: isInteractive,
            className,
          })}
          onClick={onClick}
          style={style}
          {...motionProps}
          {...(props as any)}
        >
          {badgeContent}
        </motion.span>
      );
    }

    return (
      <span
        ref={ref}
        className={badgeVariants({
          variant,
          size,
          interactive: isInteractive,
          className,
        })}
        onClick={onClick}
        {...props}
      >
        {badgeContent}
      </span>
    );
  }
);

Badge.displayName = 'Badge';
