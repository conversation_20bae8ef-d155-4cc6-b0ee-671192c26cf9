'use client';

import React from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

/**
 * Form component variants
 */
const formVariants = tv({
  base: 'space-y-6',
  variants: {
    spacing: {
      tight: 'space-y-3',
      normal: 'space-y-4',
      relaxed: 'space-y-6',
      loose: 'space-y-8',
    },
  },
  defaultVariants: {
    spacing: 'normal',
  },
});

/**
 * FormField component variants
 */
const formFieldVariants = tv({
  base: 'space-y-2',
  variants: {
    spacing: {
      tight: 'space-y-1',
      normal: 'space-y-2',
      relaxed: 'space-y-3',
    },
  },
  defaultVariants: {
    spacing: 'normal',
  },
});

/**
 * FormActions component variants
 */
const formActionsVariants = tv({
  base: 'flex gap-3',
  variants: {
    alignment: {
      left: 'justify-start',
      center: 'justify-center',
      right: 'justify-end',
      between: 'justify-between',
      stretch: 'flex-col',
    },
    spacing: {
      tight: 'gap-2',
      normal: 'gap-3',
      relaxed: 'gap-4',
    },
  },
  defaultVariants: {
    alignment: 'right',
    spacing: 'normal',
  },
});

export interface FormProps
  extends React.FormHTMLAttributes<HTMLFormElement>,
    VariantProps<typeof formVariants> {}

/**
 * Form component with consistent spacing and layout
 *
 * @example
 * ```tsx
 * <Form onSubmit={handleSubmit} spacing="relaxed">
 *   <FormField>
 *     <FormLabel>Email</FormLabel>
 *     <Input type="email" />
 *   </FormField>
 * </Form>
 * ```
 */
export const Form = React.forwardRef<HTMLFormElement, FormProps>(
  ({ className, spacing, children, ...props }, ref) => {
    return (
      <form
        ref={ref}
        className={formVariants({ spacing, className })}
        {...props}
      >
        {children}
      </form>
    );
  }
);
Form.displayName = 'Form';

export interface FormFieldProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof formFieldVariants> {}

/**
 * FormField component for wrapping form inputs with consistent spacing
 */
export const FormField = React.forwardRef<HTMLDivElement, FormFieldProps>(
  ({ className, spacing, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={formFieldVariants({ spacing, className })}
        {...props}
      >
        {children}
      </div>
    );
  }
);
FormField.displayName = 'FormField';

export interface FormLabelProps
  extends React.LabelHTMLAttributes<HTMLLabelElement> {
  /**
   * Whether the field is required
   */
  required?: boolean;
  /**
   * Custom required indicator
   */
  requiredIndicator?: React.ReactNode;
}

/**
 * FormLabel component with required indicator support
 */
export const FormLabel = React.forwardRef<HTMLLabelElement, FormLabelProps>(
  ({ className, required, requiredIndicator, children, ...props }, ref) => {
    return (
      <label
        ref={ref}
        className={`block text-sm font-medium text-primary ${className || ''}`}
        {...props}
      >
        {children}
        {required && (
          <span className='ml-1 text-brand-coral'>
            {requiredIndicator || '*'}
          </span>
        )}
      </label>
    );
  }
);
FormLabel.displayName = 'FormLabel';

export interface FormErrorProps
  extends React.HTMLAttributes<HTMLParagraphElement> {
  /**
   * Error message to display
   */
  error?: string;
}

/**
 * FormError component for displaying validation errors
 */
export const FormError = React.forwardRef<HTMLParagraphElement, FormErrorProps>(
  ({ className, error, children, ...props }, ref) => {
    const errorMessage = error || children;

    if (!errorMessage) {
      return null;
    }

    return (
      <p
        ref={ref}
        className={`text-xs text-brand-coral ${className || ''}`}
        role='alert'
        aria-live='polite'
        {...props}
      >
        {errorMessage}
      </p>
    );
  }
);
FormError.displayName = 'FormError';

export interface FormHelpProps
  extends React.HTMLAttributes<HTMLParagraphElement> {
  /**
   * Help text to display
   */
  text?: string;
}

/**
 * FormHelp component for displaying helper text
 */
export const FormHelp = React.forwardRef<HTMLParagraphElement, FormHelpProps>(
  ({ className, text, children, ...props }, ref) => {
    const helpText = text || children;

    if (!helpText) {
      return null;
    }

    return (
      <p
        ref={ref}
        className={`text-xs text-muted ${className || ''}`}
        {...props}
      >
        {helpText}
      </p>
    );
  }
);
FormHelp.displayName = 'FormHelp';

export interface FormActionsProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof formActionsVariants> {}

/**
 * FormActions component for form buttons and actions
 */
export const FormActions = React.forwardRef<HTMLDivElement, FormActionsProps>(
  ({ className, alignment, spacing, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={formActionsVariants({ alignment, spacing, className })}
        {...props}
      >
        {children}
      </div>
    );
  }
);
FormActions.displayName = 'FormActions';
