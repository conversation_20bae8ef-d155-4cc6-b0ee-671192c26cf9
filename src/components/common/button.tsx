'use client';

import { motion } from 'framer-motion';
import React from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

import type { BaseButtonProps } from '@/types';

/**
 * Button component variants using tailwind-variants
 */
export const buttonVariants = tv({
  base: [
    'inline-flex items-center justify-center gap-2 rounded-lg font-medium transition-all duration-200',
    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
    'disabled:pointer-events-none disabled:opacity-50',
    'relative overflow-hidden',
  ],
  variants: {
    variant: {
      primary: [
        'bg-brand-mint text-white shadow-sm',
        'hover:bg-brand-sage hover:shadow-md',
        'active:bg-brand-mint/90',
        'dark:bg-brand-mint dark:text-white',
        'dark:hover:bg-brand-sage',
      ],
      secondary: [
        'bg-surface border border-border text-primary shadow-sm',
        'hover:bg-elevated hover:shadow-md',
        'active:bg-surface/80',
        'dark:bg-surface dark:border-border dark:text-primary',
        'dark:hover:bg-elevated',
      ],
      outline: [
        'border border-brand-mint text-brand-mint bg-transparent',
        'hover:bg-brand-mint hover:text-white hover:shadow-md',
        'active:bg-brand-mint/90 active:text-white',
        'dark:border-brand-mint dark:text-brand-mint',
        'dark:hover:bg-brand-mint dark:hover:text-white',
      ],
      ghost: [
        'text-brand-mint bg-transparent',
        'hover:bg-brand-mint/10 hover:text-brand-mint',
        'active:bg-brand-mint/20',
        'dark:text-brand-mint',
        'dark:hover:bg-brand-mint/10',
      ],
      destructive: [
        'bg-brand-coral text-white shadow-sm',
        'hover:bg-brand-coral/90 hover:shadow-md',
        'active:bg-brand-coral/80',
        'dark:bg-brand-coral dark:text-white',
        'dark:hover:bg-brand-coral/90',
      ],
      link: [
        'text-brand-mint underline-offset-4',
        'hover:underline',
        'active:text-brand-mint/80',
        'dark:text-brand-mint',
      ],
      emphasis: [
        'bg-gradient-to-r from-brand-mint to-brand-yellow text-white shadow-md',
        'hover:shadow-lg hover:scale-105',
        'active:scale-95',
        'dark:from-brand-mint dark:to-brand-yellow',
      ],
    },
    size: {
      sm: 'h-8 px-3 text-sm',
      md: 'h-10 px-4 text-sm',
      lg: 'h-11 px-6 text-base',
      xl: 'h-12 px-8 text-base',
      icon: 'h-10 w-10 p-0',
    },
    fullWidth: {
      true: 'w-full',
    },
  },
  defaultVariants: {
    variant: 'primary',
    size: 'md',
  },
});

/**
 * Button component props with variant support
 */
export interface ButtonProps
  extends BaseButtonProps,
    VariantProps<typeof buttonVariants> {}

/**
 * Spinner component for loading state
 */
const Spinner: React.FC<{ className?: string }> = ({ className = '' }) => (
  <motion.div
    className={`inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent ${className}`}
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    transition={{ duration: 0.15 }}
  />
);

/**
 * Button component with variants, animations, and accessibility features
 *
 * A versatile button component that supports multiple visual variants, sizes, loading states,
 * and accessibility features. Built with Framer Motion for smooth animations and Tailwind CSS
 * for consistent styling across light and dark themes.
 *
 * @component
 * @example
 * // Basic usage
 * <Button variant="primary" size="lg" onClick={handleClick}>
 *   Click me
 * </Button>
 *
 * @example
 * // With loading state
 * <Button variant="outline" isLoading loadingText="Saving...">
 *   Save
 * </Button>
 *
 * @example
 * // With icons
 * <Button
 *   variant="secondary"
 *   leftIcon={<PlusIcon />}
 *   rightIcon={<ArrowIcon />}
 * >
 *   Add Item
 * </Button>
 *
 * @example
 * // Full width button
 * <Button variant="primary" fullWidth>
 *   Submit Form
 * </Button>
 *
 * @param {ButtonProps} props - The button component props
 * @param {ButtonVariant} [props.variant="primary"] - Visual style variant
 * @param {ButtonSize} [props.size="md"] - Size of the button
 * @param {boolean} [props.fullWidth=false] - Whether button takes full width
 * @param {boolean} [props.isLoading=false] - Whether button is in loading state
 * @param {string} [props.loadingText] - Text to display during loading
 * @param {React.ReactNode} [props.leftIcon] - Icon to display before text
 * @param {React.ReactNode} [props.rightIcon] - Icon to display after text
 * @param {boolean} [props.disabled=false] - Whether button is disabled
 * @param {string} [props.className] - Additional CSS classes
 * @param {React.ReactNode} props.children - Button content
 * @param {function} [props.onClick] - Click event handler
 *
 * @accessibility
 * - Supports keyboard navigation (Enter and Space keys)
 * - Provides proper ARIA attributes for screen readers
 * - Maintains focus management during loading states
 * - Follows WCAG 2.1 guidelines for color contrast
 *
 * @performance
 * - Uses Framer Motion for optimized animations
 * - Implements conditional animation props to avoid unnecessary renders
 * - Lazy loads animation properties only when needed
 *
 * @returns {React.ForwardRefExoticComponent} Forwarded ref button component
 */
export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      fullWidth,
      isLoading = false,
      loadingText,
      leftIcon,
      rightIcon,
      disabled,
      children,
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || isLoading;

    const motionProps = !isDisabled
      ? {
          whileHover: { scale: 1.02 },
          whileTap: { scale: 0.98 },
          transition: { type: 'spring' as const, stiffness: 400, damping: 17 },
        }
      : {};

    return (
      <motion.button
        ref={ref}
        className={buttonVariants({ variant, size, fullWidth, className })}
        disabled={isDisabled}
        {...motionProps}
        {...props}
      >
        {isLoading && <Spinner />}
        {!isLoading && leftIcon && (
          <motion.span
            initial={{ opacity: 0, x: -4 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.2 }}
          >
            {leftIcon}
          </motion.span>
        )}

        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        >
          {isLoading && loadingText ? loadingText : children}
        </motion.span>

        {!isLoading && rightIcon && (
          <motion.span
            initial={{ opacity: 0, x: 4 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.2 }}
          >
            {rightIcon}
          </motion.span>
        )}
      </motion.button>
    );
  }
);

Button.displayName = 'Button';
