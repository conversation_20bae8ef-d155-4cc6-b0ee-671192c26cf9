'use client';

import { motion } from 'framer-motion';
import React from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

/**
 * Spinner component variants using tailwind-variants
 */
export const spinnerVariants = tv({
  base: [
    'animate-spin rounded-full border-2 border-solid border-current border-r-transparent',
    'inline-block',
  ],
  variants: {
    size: {
      xs: 'h-3 w-3 border-[1.5px]',
      sm: 'h-4 w-4 border-2',
      md: 'h-6 w-6 border-2',
      lg: 'h-8 w-8 border-2',
      xl: 'h-12 w-12 border-[3px]',
    },
    variant: {
      default: 'text-primary',
      primary: 'text-brand-mint',
      secondary: 'text-muted',
      white: 'text-white',
      current: 'text-current',
    },
  },
  defaultVariants: {
    size: 'md',
    variant: 'default',
  },
});

export interface SpinnerProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'children'>,
    VariantProps<typeof spinnerVariants> {
  /**
   * Label for screen readers
   */
  label?: string;
  /**
   * Whether to show the spinner with a fade-in animation
   */
  animate?: boolean;
}

/**
 * Spinner component for loading states
 *
 * @example
 * ```tsx
 * <Spinner size="lg" variant="primary" />
 * <Spinner label="Loading content..." />
 * ```
 */
export const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  (
    {
      className,
      size,
      variant,
      label = 'Loading...',
      animate = true,
      ...props
    },
    ref
  ) => {
    const spinnerElement = (
      <div
        className={spinnerVariants({ size, variant, className })}
        role='status'
        aria-label={label}
        {...props}
      />
    );

    if (animate) {
      return (
        <motion.div
          ref={ref}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          {spinnerElement}
        </motion.div>
      );
    }

    return <div ref={ref}>{spinnerElement}</div>;
  }
);

Spinner.displayName = 'Spinner';

/**
 * Spinner overlay component for full-screen loading states
 */
export interface SpinnerOverlayProps extends SpinnerProps {
  /**
   * Whether the overlay is visible
   */
  visible?: boolean;
  /**
   * Text to display below the spinner
   */
  text?: string;
  /**
   * Background opacity (0-100)
   */
  backgroundOpacity?: number;
}

export const SpinnerOverlay: React.FC<SpinnerOverlayProps> = ({
  visible = true,
  text,
  backgroundOpacity = 50,
  size = 'xl',
  variant = 'primary',
  ...spinnerProps
}) => {
  if (!visible) {
    return null;
  }

  return (
    <motion.div
      className='fixed inset-0 z-50 flex items-center justify-center bg-overlay'
      style={{
        // Use CSS variable based overlay color; allow opacity prop via inline alpha variable if defined
        ['--overlay-opacity' as any]: backgroundOpacity / 100,
      }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
    >
      <motion.div
        className='flex flex-col items-center gap-4 p-6 rounded-lg bg-surface border border-border shadow-lg'
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.2, delay: 0.1 }}
      >
        <Spinner
          size={size}
          variant={variant}
          animate={false}
          {...spinnerProps}
        />
        {text && (
          <p className='text-sm text-muted text-center max-w-xs'>{text}</p>
        )}
      </motion.div>
    </motion.div>
  );
};

SpinnerOverlay.displayName = 'SpinnerOverlay';
