/**
 * Utility types for component development
 */

import type React from 'react';

/**
 * Extract variant props from tailwind-variants config
 */
export type VariantProps<T> = T extends (...args: any[]) => any
  ? Parameters<T>[0]
  : never;

/**
 * Make certain properties optional
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Make certain properties required
 */
export type RequiredBy<T, K extends keyof T> = Omit<T, K> &
  Required<Pick<T, K>>;

/**
 * Merge two types, with the second type taking precedence
 */
export type Merge<T, U> = Omit<T, keyof U> & U;

/**
 * Extract HTML element props
 */
export type HTMLElementProps<T extends keyof React.JSX.IntrinsicElements> =
  React.JSX.IntrinsicElements[T];

/**
 * Component with style
 */
export type WithStyle<T = {}> = T & {
  style?: React.CSSProperties;
};

/**
 * Component with data attributes
 */
export type WithDataAttributes<T = {}> = T & {
  [key: `data-${string}`]: string | number | boolean | undefined;
};

/**
 * Event handler types
 */
export type EventHandler<T = Element, E = Event> = (
  event: E & { target: T }
) => void;

/**
 * Mouse event handler
 */
export type MouseEventHandler<T = Element> = EventHandler<T, React.MouseEvent>;

/**
 * Keyboard event handler
 */
export type KeyboardEventHandler<T = Element> = EventHandler<
  T,
  React.KeyboardEvent
>;

/**
 * Focus event handler
 */
export type FocusEventHandler<T = Element> = EventHandler<T, React.FocusEvent>;

/**
 * Change event handler
 */
export type ChangeEventHandler<T = Element> = EventHandler<
  T,
  React.ChangeEvent
>;

/**
 * Form event handler
 */
export type FormEventHandler<T = Element> = EventHandler<T, React.FormEvent>;

/**
 * Generic callback function
 */
export type Callback<T = void> = () => T;

/**
 * Async callback function
 */
export type AsyncCallback<T = void> = () => Promise<T>;

/**
 * Value change callback
 */
export type ValueChangeCallback<T> = (value: T) => void;

/**
 * Validation function
 */
export type ValidationFunction<T> = (value: T) => string | null | undefined;

/**
 * Theme mode type
 */
export type ThemeMode = 'light' | 'dark' | 'system';

/**
 * Responsive value type
 */
export type ResponsiveValue<T> =
  | T
  | {
      base?: T;
      sm?: T;
      md?: T;
      lg?: T;
      xl?: T;
      '2xl'?: T;
    };
