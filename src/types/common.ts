/**
 * Common types and utilities used across components
 */

import type React from 'react';

/**
 * Base size variants used across multiple components
 */
export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

/**
 * Common spacing variants
 */
export type Spacing = 'tight' | 'normal' | 'relaxed' | 'loose';

/**
 * Common state variants for form elements
 */
export type State = 'default' | 'error' | 'success' | 'warning';

/**
 * Common shape variants
 */
export type Shape = 'circle' | 'square' | 'rounded';

/**
 * Common color variants
 */
export type ColorVariant =
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'info';

/**
 * Animation configuration
 */
export interface AnimationConfig {
  /** Whether to enable animations */
  enabled?: boolean;
  /** Animation duration in milliseconds */
  duration?: number;
  /** Animation easing function */
  easing?: string;
}

/**
 * Icon props interface
 */
export interface IconProps {
  /** Icon element to display */
  icon?: React.ReactNode;
  /** Icon position */
  iconPosition?: 'left' | 'right' | 'start' | 'end';
}

/**
 * Loading state interface
 */
export interface LoadingProps {
  /** Whether the component is in loading state */
  isLoading?: boolean;
  /** Text to display during loading */
  loadingText?: string;
  /** Custom loading indicator */
  loadingIndicator?: React.ReactNode;
}

/**
 * Accessibility props
 */
export interface AccessibilityProps {
  /** ARIA label */
  'aria-label'?: string;
  /** ARIA described by */
  'aria-describedby'?: string;
  /** ARIA labelled by */
  'aria-labelledby'?: string;
  /** ARIA expanded state */
  'aria-expanded'?: boolean;
  /** ARIA disabled state */
  'aria-disabled'?: boolean;
}

/**
 * Interactive element props
 */
export interface InteractiveProps {
  /** Whether the element is interactive/clickable */
  interactive?: boolean;
  /** Whether the element is disabled */
  disabled?: boolean;
  /** Click handler */
  onClick?: (event: React.MouseEvent) => void;
}

/**
 * Polymorphic component props
 */
export type PolymorphicProps<T extends React.ElementType> = {
  /** The element type to render as */
  as?: T;
} & React.ComponentPropsWithoutRef<T>;

/**
 * Component with children
 */
export type WithChildren<T = {}> = T & {
  children?: React.ReactNode;
};

/**
 * Component with className
 */
export type WithClassName<T = {}> = T & {
  className?: string;
};

/**
 * Ref forwarding utility type
 */
export type ForwardRefComponent<T, P = {}> = React.ForwardRefExoticComponent<
  React.PropsWithoutRef<P> & React.RefAttributes<T>
>;
