/**
 * Variant type definitions for component styling
 */

/**
 * Button variant types
 */
export type ButtonVariant =
  | 'primary'
  | 'secondary'
  | 'outline'
  | 'ghost'
  | 'destructive'
  | 'link'
  | 'emphasis';

/**
 * Button size types
 */
export type ButtonSize = 'sm' | 'md' | 'lg' | 'xl' | 'icon';

/**
 * Input variant types
 */
export type InputVariant = 'default' | 'filled' | 'outline';

/**
 * Input size types
 */
export type InputSize = 'sm' | 'md' | 'lg';

/**
 * Card variant types
 */
export type CardVariant =
  | 'default'
  | 'elevated'
  | 'outline'
  | 'ghost'
  | 'gradient';

/**
 * Badge variant types
 */
export type BadgeVariant =
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'outline';

/**
 * Badge size types
 */
export type BadgeSize = 'sm' | 'md' | 'lg';

/**
 * Avatar variant types
 */
export type AvatarVariant = 'default' | 'primary' | 'secondary' | 'outline';

/**
 * Avatar size types
 */
export type AvatarSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

/**
 * Avatar shape types
 */
export type AvatarShape = 'circle' | 'square';

/**
 * Spinner variant types
 */
export type SpinnerVariant =
  | 'default'
  | 'primary'
  | 'secondary'
  | 'white'
  | 'current';

/**
 * Spinner size types
 */
export type SpinnerSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

/**
 * Container size types
 */
export type ContainerSize = 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';

/**
 * Stack direction types
 */
export type StackDirection = 'row' | 'column';

/**
 * Stack alignment types
 */
export type StackAlign = 'start' | 'center' | 'end' | 'stretch';

/**
 * Stack justify types
 */
export type StackJustify =
  | 'start'
  | 'center'
  | 'end'
  | 'between'
  | 'around'
  | 'evenly';

/**
 * Grid columns types
 */
export type GridColumns = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;

/**
 * Modal size types
 */
export type ModalSize =
  | 'sm'
  | 'md'
  | 'lg'
  | 'xl'
  | '2xl'
  | '3xl'
  | '4xl'
  | 'full';

/**
 * Form spacing types
 */
export type FormSpacing = 'tight' | 'normal' | 'relaxed' | 'loose';

/**
 * Status indicator types
 */
export type StatusType = 'online' | 'offline' | 'away' | 'busy';

/**
 * Animation preset types
 */
export type AnimationPreset =
  | 'fade'
  | 'slide'
  | 'scale'
  | 'bounce'
  | 'spin'
  | 'pulse'
  | 'none';

/**
 * Elevation levels for shadows
 */
export type ElevationLevel = 0 | 1 | 2 | 3 | 4 | 5;
