'use client';

import React from 'react';

import {
  Ava<PERSON>,
  AvatarGroup,
  Badge,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Container,
  Divider,
  Flex,
  Form,
  FormActions,
  FormField,
  FormLabel,
  Grid,
  Input,
  Modal,
  ModalBody,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalTitle,
  <PERSON>ner,
  Stack,
} from '@/components/common';

export default function ComponentsDemo() {
  const [isModalOpen, setIsModalOpen] = React.useState(false);

  return (
    <Container size='xl' className='py-8'>
      <Stack spacing='xl'>
        {/* Header */}
        <div className='text-center'>
          <h1 className='text-4xl font-bold text-primary mb-4'>
            CresceFeliz UI Components
          </h1>
          <p className='text-lg text-muted max-w-2xl mx-auto'>
            A comprehensive component library built with React, TypeScript, and
            Tailwind CSS. Featuring dark mode support, animations, and
            accessibility.
          </p>
        </div>

        <Divider />

        {/* Buttons */}
        <Card>
          <CardHeader>
            <CardTitle>Buttons</CardTitle>
          </CardHeader>
          <CardContent>
            <Stack spacing='lg'>
              <div>
                <h4 className='text-sm font-medium text-muted mb-3'>
                  Variants
                </h4>
                <Flex spacing='md' wrap>
                  <Button variant='primary'>Primary</Button>
                  <Button variant='secondary'>Secondary</Button>
                  <Button variant='outline'>Outline</Button>
                  <Button variant='ghost'>Ghost</Button>
                  <Button variant='destructive'>Destructive</Button>
                  <Button variant='link'>Link</Button>
                </Flex>
              </div>

              <div>
                <h4 className='text-sm font-medium text-muted mb-3'>Sizes</h4>
                <Flex spacing='md' align='center'>
                  <Button size='sm'>Small</Button>
                  <Button size='md'>Medium</Button>
                  <Button size='lg'>Large</Button>
                  <Button size='xl'>Extra Large</Button>
                </Flex>
              </div>

              <div>
                <h4 className='text-sm font-medium text-muted mb-3'>States</h4>
                <Flex spacing='md'>
                  <Button isLoading loadingText='Loading...'>
                    Loading
                  </Button>
                  <Button disabled>Disabled</Button>
                  <Button fullWidth>Full Width</Button>
                </Flex>
              </div>
            </Stack>
          </CardContent>
        </Card>

        {/* Inputs */}
        <Card>
          <CardHeader>
            <CardTitle>Inputs</CardTitle>
          </CardHeader>
          <CardContent>
            <Grid cols={2} gap='lg'>
              <Input label='Default Input' placeholder='Enter text...' />
              <Input
                label='Filled Input'
                variant='filled'
                placeholder='Filled variant'
              />
              <Input
                label='Outline Input'
                variant='outline'
                placeholder='Outline variant'
              />
              <Input
                label='Input with Error'
                error='This field is required'
                placeholder='Error state'
              />
              <Input
                label='Input with Success'
                success='Looks good!'
                placeholder='Success state'
              />
              <Input
                label='Required Input'
                required
                helperText='This field is required'
                placeholder='Required field'
              />
            </Grid>
          </CardContent>
        </Card>

        {/* Badges */}
        <Card>
          <CardHeader>
            <CardTitle>Badges</CardTitle>
          </CardHeader>
          <CardContent>
            <Stack spacing='md'>
              <div>
                <h4 className='text-sm font-medium text-muted mb-3'>
                  Variants
                </h4>
                <Flex spacing='md' wrap>
                  <Badge variant='default'>Default</Badge>
                  <Badge variant='primary'>Primary</Badge>
                  <Badge variant='secondary'>Secondary</Badge>
                  <Badge variant='success'>Success</Badge>
                  <Badge variant='warning'>Warning</Badge>
                  <Badge variant='error'>Error</Badge>
                  <Badge variant='outline'>Outline</Badge>
                </Flex>
              </div>

              <div>
                <h4 className='text-sm font-medium text-muted mb-3'>
                  Interactive
                </h4>
                <Flex spacing='md'>
                  <Badge variant='primary' interactive>
                    Clickable
                  </Badge>
                  <Badge variant='warning' removable>
                    Removable
                  </Badge>
                </Flex>
              </div>
            </Stack>
          </CardContent>
        </Card>

        {/* Avatars */}
        <Card>
          <CardHeader>
            <CardTitle>Avatars</CardTitle>
          </CardHeader>
          <CardContent>
            <Stack spacing='md'>
              <div>
                <h4 className='text-sm font-medium text-muted mb-3'>Sizes</h4>
                <Flex spacing='md' align='center'>
                  <Avatar size='xs' fallback='XS' />
                  <Avatar size='sm' fallback='SM' />
                  <Avatar size='md' fallback='MD' />
                  <Avatar size='lg' fallback='LG' />
                  <Avatar size='xl' fallback='XL' />
                  <Avatar size='2xl' fallback='2XL' />
                </Flex>
              </div>

              <div>
                <h4 className='text-sm font-medium text-muted mb-3'>
                  Variants & Status
                </h4>
                <Flex spacing='md' align='center'>
                  <Avatar variant='primary' fallback='PR' />
                  <Avatar variant='secondary' fallback='SE' />
                  <Avatar fallback='ON' status='online' />
                  <Avatar fallback='OF' status='offline' />
                  <Avatar fallback='AW' status='away' />
                  <Avatar fallback='BU' status='busy' />
                </Flex>
              </div>

              <div>
                <h4 className='text-sm font-medium text-muted mb-3'>
                  Avatar Group
                </h4>
                <AvatarGroup max={3}>
                  <Avatar fallback='A1' />
                  <Avatar fallback='A2' />
                  <Avatar fallback='A3' />
                  <Avatar fallback='A4' />
                  <Avatar fallback='A5' />
                </AvatarGroup>
              </div>
            </Stack>
          </CardContent>
        </Card>

        {/* Spinners */}
        <Card>
          <CardHeader>
            <CardTitle>Spinners</CardTitle>
          </CardHeader>
          <CardContent>
            <Stack spacing='md'>
              <div>
                <h4 className='text-sm font-medium text-muted mb-3'>Sizes</h4>
                <Flex spacing='md' align='center'>
                  <Spinner size='xs' />
                  <Spinner size='sm' />
                  <Spinner size='md' />
                  <Spinner size='lg' />
                  <Spinner size='xl' />
                </Flex>
              </div>

              <div>
                <h4 className='text-sm font-medium text-muted mb-3'>
                  Variants
                </h4>
                <Flex spacing='md' align='center'>
                  <Spinner variant='default' />
                  <Spinner variant='primary' />
                  <Spinner variant='secondary' />
                </Flex>
              </div>
            </Stack>
          </CardContent>
        </Card>

        {/* Layout Components */}
        <Card>
          <CardHeader>
            <CardTitle>Layout Components</CardTitle>
          </CardHeader>
          <CardContent>
            <Stack spacing='lg'>
              <div>
                <h4 className='text-sm font-medium text-muted mb-3'>
                  Grid Layout
                </h4>
                <Grid cols={3} gap='md'>
                  <div className='bg-surface border border-border rounded p-4 text-center'>
                    Grid Item 1
                  </div>
                  <div className='bg-surface border border-border rounded p-4 text-center'>
                    Grid Item 2
                  </div>
                  <div className='bg-surface border border-border rounded p-4 text-center'>
                    Grid Item 3
                  </div>
                </Grid>
              </div>

              <div>
                <h4 className='text-sm font-medium text-muted mb-3'>
                  Stack Layout
                </h4>
                <Stack spacing='sm'>
                  <div className='bg-surface border border-border rounded p-3'>
                    Stack Item 1
                  </div>
                  <div className='bg-surface border border-border rounded p-3'>
                    Stack Item 2
                  </div>
                  <div className='bg-surface border border-border rounded p-3'>
                    Stack Item 3
                  </div>
                </Stack>
              </div>
            </Stack>
          </CardContent>
        </Card>

        {/* Form Example */}
        <Card>
          <CardHeader>
            <CardTitle>Form Components</CardTitle>
          </CardHeader>
          <CardContent>
            <Form>
              <Grid cols={2} gap='lg'>
                <FormField>
                  <FormLabel required>First Name</FormLabel>
                  <Input placeholder='Enter first name' />
                </FormField>

                <FormField>
                  <FormLabel required>Last Name</FormLabel>
                  <Input placeholder='Enter last name' />
                </FormField>
              </Grid>

              <FormField>
                <FormLabel required>Email</FormLabel>
                <Input type='email' placeholder='Enter email address' />
              </FormField>

              <FormActions>
                <Button variant='outline' type='button'>
                  Cancel
                </Button>
                <Button type='submit'>Submit</Button>
              </FormActions>
            </Form>
          </CardContent>
        </Card>

        {/* Modal Example */}
        <Card>
          <CardHeader>
            <CardTitle>Modal Component</CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={() => setIsModalOpen(true)}>Open Modal</Button>
          </CardContent>
        </Card>
      </Stack>

      {/* Modal */}
      <Modal open={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <ModalHeader>
          <ModalTitle>Example Modal</ModalTitle>
        </ModalHeader>
        <ModalBody>
          <p className='text-muted'>
            This is an example modal dialog. It demonstrates the modal component
            with proper accessibility features and animations.
          </p>
        </ModalBody>
        <ModalFooter>
          <Button variant='outline' onClick={() => setIsModalOpen(false)}>
            Cancel
          </Button>
          <Button onClick={() => setIsModalOpen(false)}>Confirm</Button>
        </ModalFooter>
      </Modal>
    </Container>
  );
}
