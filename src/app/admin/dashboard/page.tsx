'use client';

import { useState } from 'react';
import {
  BsBarChart,
  BsBell,
  BsBoxArrowRight,
  BsBuilding,
  BsCalendar3,
  BsCamera,
  BsChevronDown,
  BsExclamationCircle,
  BsFileText,
  BsGear,
  BsHouseDoor,
  BsImage,
  BsList,
  BsPeople,
  BsSearch,
  BsShield,
  BsX,
} from 'react-icons/bs';

import { Button } from '../../../components/common';

export default function AdminDashboard() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);

  const stats = [
    {
      name: 'Total de Escolas',
      value: '24',
      icon: BsBuilding,
      color: 'bg-green-500',
      change: '+12%',
    },
    {
      name: 'Utilizadores Ativos',
      value: '1,847',
      icon: BsPeople,
      color: 'bg-yellow-500',
      change: '+8%',
    },
    {
      name: 'Fotografias Hoje',
      value: '342',
      icon: BsCamera,
      color: 'bg-emerald-500',
      change: '+23%',
    },
    {
      name: 'Eventos Ativos',
      value: '18',
      icon: BsCalendar3,
      color: 'bg-amber-500',
      change: '+5%',
    },
  ];

  const recentActivities = [
    {
      title: 'Nova escola registada',
      description: 'Jardim Infantil Arco-Íris',
      time: '2 min atrás',
      type: 'success',
    },
    {
      title: 'Fotografias aprovadas',
      description: '15 fotografias do evento "Festa da Primavera"',
      time: '15 min atrás',
      type: 'info',
    },
    {
      title: 'Utilizador reportado',
      description: 'Necessita de revisão manual',
      time: '1 hora atrás',
      type: 'warning',
    },
    {
      title: 'Backup concluído',
      description: 'Backup diário concluído com sucesso',
      time: '3 horas atrás',
      type: 'success',
    },
  ];

  const sidebarItems = [
    {
      name: 'Dashboard',
      icon: BsHouseDoor,
      href: '/admin/dashboard',
      current: true,
    },
    {
      name: 'Escolas',
      icon: BsBuilding,
      href: '/admin/schools',
      current: false,
    },
    {
      name: 'Utilizadores',
      icon: BsPeople,
      href: '/admin/users',
      current: false,
    },
    {
      name: 'Fotografias',
      icon: BsImage,
      href: '/admin/photos',
      current: false,
    },
    {
      name: 'Eventos',
      icon: BsCalendar3,
      href: '/admin/events',
      current: false,
    },
    {
      name: 'Relatórios',
      icon: BsFileText,
      href: '/admin/reports',
      current: false,
    },
    {
      name: 'Segurança',
      icon: BsShield,
      href: '/admin/security',
      current: false,
    },
    {
      name: 'Configurações',
      icon: BsGear,
      href: '/admin/settings',
      current: false,
    },
  ];

  return (
    <div className='min-h-screen bg-brand-cream'>
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className='fixed inset-0 z-40 bg-brand-gray/60 bg-opacity-75 lg:hidden'
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}
      >
        <div className='flex items-center justify-between h-16 px-4 border-b border-brand-sage/40 bg-surface/80 backdrop-blur-sm'>
          <div className='flex items-center'>
            <div className='flex items-center justify-center w-8 h-8 mr-3 rounded-lg bg-brand-gradient'>
              <BsBuilding className='w-5 h-5 text-white' />
            </div>
            <h1 className='text-xl font-bold text-primary'>Cresce Feliz</h1>
          </div>
          <Button
            onClick={() => setSidebarOpen(false)}
            variant='outline'
            size='icon'
            className='lg:hidden'
          >
            <BsX className='w-5 h-5' />
          </Button>
        </div>

        <nav className='px-4 mt-8'>
          <ul className='space-y-2'>
            {sidebarItems.map((item) => (
              <li key={item.name}>
                <a
                  href={item.href}
                  className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                    item.current
                      ? 'bg-brand-sage/40 text-brand-mint border-r-2 border-brand-mint'
                      : 'text-brand-gray hover:bg-brand-sage/30 hover:text-brand-mint'
                  }`}
                >
                  <item.icon className='w-5 h-5 mr-3' />
                  {item.name}
                </a>
              </li>
            ))}
          </ul>
        </nav>
      </div>

      {/* Main content */}
      <div className='lg:ml-64'>
        {/* Header */}
        <header className='bg-surface/80 backdrop-blur border-b border-brand-sage/40 shadow-sm'>
          <div className='flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8'>
            <div className='flex items-center'>
              <Button
                onClick={() => setSidebarOpen(true)}
                variant='outline'
                size='icon'
                className='lg:hidden'
              >
                <BsList className='w-5 h-5' />
              </Button>
              <h1 className='ml-4 text-2xl font-semibold text-primary lg:ml-0'>
                Dashboard
              </h1>
            </div>

            <div className='flex items-center space-x-4'>
              {/* Search */}
              <div className='relative hidden md:block'>
                <BsSearch className='absolute w-4 h-4 text-brand-gray transform -translate-y-1/2 left-3 top-1/2' />
                <input
                  type='text'
                  placeholder='Pesquisar...'
                  className='w-64 py-2 pl-10 pr-4 border rounded-lg border-brand-sage focus:ring-2 focus:ring-brand-mint focus:border-transparent bg-white/60 placeholder:text-brand-gray/70'
                />
              </div>

              {/* Notifications */}
              <Button variant='outline' size='icon' className='relative'>
                <BsBell className='w-5 h-5' />
                <span className='absolute top-0 right-0 w-2 h-2 rounded-full bg-brand-coral' />
              </Button>

              {/* User dropdown */}
              <div className='relative'>
                <button
                  onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                  className='flex items-center p-2 space-x-2 rounded-lg hover:bg-brand-sage/30'
                >
                  <div className='flex items-center justify-center w-8 h-8 rounded-full bg-brand-gradient'>
                    <span className='text-sm font-medium text-white'>A</span>
                  </div>
                  <BsChevronDown className='w-4 h-4 text-gray-500' />
                </button>

                {userDropdownOpen && (
                  <div className='absolute right-0 z-50 w-48 mt-2 rounded-lg shadow-lg bg-surface border border-brand-sage/40 backdrop-blur'>
                    <div className='px-4 py-3 border-b border-brand-sage/30'>
                      <p className='text-sm font-medium text-primary'>
                        Administrador
                      </p>
                      <p className='text-sm text-brand-gray'>
                        <EMAIL>
                      </p>
                    </div>
                    <div className='py-2'>
                      <a
                        href='#'
                        className='flex items-center px-4 py-2 text-sm text-brand-gray hover:bg-brand-sage/30 hover:text-brand-mint'
                      >
                        <BsGear className='w-4 h-4 mr-2' />
                        Configurações
                      </a>
                      <a
                        href='/admin/login'
                        className='flex items-center px-4 py-2 text-sm text-brand-gray hover:bg-brand-sage/30 hover:text-brand-mint'
                      >
                        <BsBoxArrowRight className='w-4 h-4 mr-2' />
                        Terminar sessão
                      </a>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Main content area */}
        <main className='p-6'>
          {/* Stats Cards */}
          <div className='grid grid-cols-1 gap-6 mb-8 md:grid-cols-2 lg:grid-cols-4'>
            {stats.map((stat) => (
              <div
                key={stat.name}
                className='p-6 rounded-xl shadow-sm border border-brand-sage/40 bg-surface/80 backdrop-blur'
              >
                <div className='flex items-center justify-between'>
                  <div>
                    <p className='text-sm font-medium text-brand-gray'>
                      {stat.name}
                    </p>
                    <p className='mt-1 text-3xl font-bold text-primary'>
                      {stat.value}
                    </p>
                    <div className='flex items-center mt-2'>
                      <span className='w-4 h-4 mr-1 text-brand-mint'>↗</span>
                      <span className='text-sm text-brand-mint'>
                        {stat.change}
                      </span>
                    </div>
                  </div>
                  <div className='p-3 rounded-lg bg-brand-mint'>
                    <stat.icon className='w-6 h-6 text-white' />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Charts and Recent Activity */}
          <div className='grid grid-cols-1 gap-6 lg:grid-cols-2'>
            {/* Chart placeholder */}
            <div className='p-6 rounded-xl shadow-sm border border-brand-sage/40 bg-surface/80 backdrop-blur'>
              <div className='flex items-center justify-between mb-4'>
                <h3 className='text-lg font-semibold text-primary'>
                  Atividade Mensal
                </h3>
                <BsBarChart className='w-5 h-5 text-brand-gray' />
              </div>
              <div className='flex items-center justify-center h-64 rounded-lg bg-brand-sage/30'>
                <div className='text-center'>
                  <BsBarChart className='w-12 h-12 mx-auto mb-2 text-brand-gray' />
                  <p className='text-brand-gray'>Gráfico de atividade</p>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className='p-6 rounded-xl shadow-sm border border-brand-sage/40 bg-surface/80 backdrop-blur'>
              <h3 className='mb-4 text-lg font-semibold text-primary'>
                Atividade Recente
              </h3>
              <div className='space-y-4'>
                {recentActivities.map((activity, index) => (
                  <div key={index} className='flex items-start space-x-3'>
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                        activity.type === 'success'
                          ? 'bg-green-100'
                          : activity.type === 'warning'
                            ? 'bg-yellow-100'
                            : 'bg-blue-100'
                      }`}
                    >
                      <BsExclamationCircle
                        className={`w-4 h-4 ${
                          activity.type === 'success'
                            ? 'text-green-600'
                            : activity.type === 'warning'
                              ? 'text-yellow-600'
                              : 'text-blue-600'
                        }`}
                      />
                    </div>
                    <div className='flex-1 min-w-0'>
                      <p className='text-sm font-medium text-primary'>
                        {activity.title}
                      </p>
                      <p className='text-sm text-brand-gray'>
                        {activity.description}
                      </p>
                      <p className='mt-1 text-xs text-brand-gray/70'>
                        {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
