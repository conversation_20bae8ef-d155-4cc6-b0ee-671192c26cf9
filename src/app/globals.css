@import 'tailwindcss';

/* Light theme (default) */
:root {
  /* Legacy tokens */
  --background: #ffffff;
  --foreground: #171717;

  /* Brand palette tokens */
  --brand-mint: #7acbc1; /* Primary */
  --brand-yellow: #f6c75a; /* Accent */
  --brand-sage: #a8deda; /* Light variant / hover */
  --brand-cream: #f5efe9; /* Background neutral */
  --brand-gray: #a8a096; /* Text secondary */
  --brand-coral: #ef8a7e; /* Attention */

  /* Semantic surface + text tokens */
  --color-bg: var(--brand-cream);
  --color-bg-surface: rgba(255, 255, 255, 0.7);
  --color-bg-elevated: #ffffff;
  --color-border: var(--brand-sage);
  --color-ring: var(--brand-mint);
  --color-text-primary: #1f2937; /* slate-800 */
  --color-text-muted: #6b7280; /* gray-500 */
  --color-text-invert: #ffffff;
  --color-focus-outline: var(--brand-mint);

  /* Semantic status tokens (mapped to brand hues) */
  --color-success: var(--brand-mint);
  --color-warning: var(--brand-yellow);
  --color-error: var(--brand-coral);
  /* Overlay base (used with alpha in utilities) */
  --color-overlay: 0 0 0;
}

/* Font variable mappings consumed by Tailwind theme.extend.fontFamily */
:root {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Dark theme (class based) */
.dark,
[data-theme='dark'] {
  --background: #0a0a0a;
  --foreground: #ededed;

  /* Brand palette adjustments for dark mode (slightly tuned for contrast) */
  --brand-mint: #7acbc1; /* Keeps identity */
  --brand-yellow: #d8ad49; /* Slightly muted to reduce luminance glow */
  --brand-sage: #79c2bc; /* Deeper variant for hover visibility */
  --brand-cream: #0f1514; /* App background */
  --brand-gray: #cfc8bf; /* Lightened for readability */
  --brand-coral: #f08d81; /* Slight tweak for dark contrast */

  --color-bg: var(--brand-cream);
  --color-bg-surface: #182220;
  --color-bg-elevated: #1f2b29;
  --color-border: #27413d;
  --color-ring: var(--brand-mint);
  --color-text-primary: #f3f4f6; /* gray-100 */
  --color-text-muted: #9ca3af; /* gray-400 */
  --color-text-invert: #0f1514;
  --color-focus-outline: var(--brand-mint);

  --color-success: var(--brand-mint);
  --color-warning: var(--brand-yellow);
  --color-error: var(--brand-coral);
  --color-overlay: 0 0 0; /* still black, could adapt for dark if needed */
}

body {
  background: var(--color-bg);
  color: var(--color-text-primary);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}

/* Utility classes bridging to variables (optional future replacement for direct tailwind classes) */
.bg-app-surface {
  background: var(--color-bg-surface);
}
.bg-app-elevated {
  background: var(--color-bg-elevated);
}
.text-muted {
  color: var(--color-text-muted);
}
.border-brand {
  border-color: var(--color-border);
}
.ring-brand {
  --tw-ring-color: var(--color-ring);
}
.focus-outline-brand:focus-visible {
  outline: 2px solid var(--color-focus-outline);
  outline-offset: 2px;
}

/* Brand gradient utility */
.bg-brand-gradient {
  background-image: linear-gradient(
    to right,
    var(--brand-mint),
    var(--brand-yellow)
  );
}

/* Overlay utility (expects optional inline --overlay-opacity 0-1). Fallback 0.5 */
.bg-overlay {
  background-color: rgba(var(--color-overlay) / var(--overlay-opacity, 0.5));
}

/* Semantic text helpers */
.text-success {
  color: var(--color-success);
}
.text-warning {
  color: var(--color-warning);
}
.text-error {
  color: var(--color-error);
}

/* Semantic border helpers */
.border-success {
  border-color: var(--color-success);
}
.border-warning {
  border-color: var(--color-warning);
}
.border-error {
  border-color: var(--color-error);
}

/* Semantic background subtle helpers */
.bg-success-subtle {
  background-color: color-mix(in srgb, var(--color-success) 12%, transparent);
}
.bg-warning-subtle {
  background-color: color-mix(in srgb, var(--color-warning) 15%, transparent);
}
.bg-error-subtle {
  background-color: color-mix(in srgb, var(--color-error) 15%, transparent);
}
